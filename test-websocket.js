/**
 * WebSocket Connection Test Script
 * Tests the application WebSocket connection (not webpack-dev-server)
 */

// Test WebSocket connection to the application backend
function testApplicationWebSocket() {
    console.log('🧪 Testing Application WebSocket Connection...');
    
    // Use the same logic as the application
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const hostname = window.location.hostname;
    
    // For dev tunnels, don't include port
    let wsUrl;
    if (hostname.includes('devtunnels.ms')) {
        wsUrl = `${protocol}//${hostname}/ws`;
    } else {
        wsUrl = `${protocol}//${hostname}:5000`;
    }
    
    console.log('🔗 Attempting to connect to:', wsUrl);
    
    const ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
        console.log('✅ Application WebSocket connected successfully!');
        
        // Send test authentication message
        ws.send(JSON.stringify({
            type: 'test',
            message: 'Test connection from browser console'
        }));
        
        // Close after 5 seconds
        setTimeout(() => {
            ws.close(1000, 'Test completed');
        }, 5000);
    };
    
    ws.onmessage = (event) => {
        console.log('📥 Received message:', event.data);
    };
    
    ws.onerror = (error) => {
        console.error('❌ Application WebSocket error:', error);
    };
    
    ws.onclose = (event) => {
        console.log('🔌 Application WebSocket closed:', event.code, event.reason);
    };
}

// Test function that can be called from browser console
window.testAppWebSocket = testApplicationWebSocket;

console.log('🧪 WebSocket test script loaded. Run testAppWebSocket() to test the connection.');
