const { createProxyMiddleware } = require('http-proxy-middleware');
const os = require('os');

// Get local network IP automatically
function getLocalNetworkIP() {
  const interfaces = os.networkInterfaces();
  for (const [name, addresses] of Object.entries(interfaces)) {
    for (const addr of addresses) {
      if (!addr.internal && addr.family === 'IPv4' &&
          !name.toLowerCase().includes('vmware') &&
          !name.toLowerCase().includes('virtualbox')) {
        return addr.address;
      }
    }
  }
  return 'localhost';
}

// This file configures the development server's proxy settings
module.exports = function(app) {
  // Enhanced dev tunnel detection
  const isDevTunnel = process.env.CODESPACES ||
                     process.env.GITPOD_WORKSPACE_ID ||
                     process.env.VSCODE_TUNNEL ||
                     (process.env.HOST && process.env.HOST.includes('devtunnels.ms')) ||
                     (process.env.PUBLIC_URL && process.env.PUBLIC_URL.includes('devtunnels.ms')) ||
                     (process.env.REACT_APP_DEV_TUNNEL === 'true') ||
                     // Check if we're running on a dev tunnel port pattern
                     (process.env.PORT && process.env.PORT === '3000' &&
                      (process.env.HOST === '0.0.0.0' || process.env.DANGEROUSLY_DISABLE_HOST_CHECK === 'true'));

  console.log('🔧 Proxy Setup - Dev Tunnel Environment:', isDevTunnel);
  console.log('🔧 Environment variables:', {
    CODESPACES: process.env.CODESPACES,
    GITPOD_WORKSPACE_ID: process.env.GITPOD_WORKSPACE_ID,
    VSCODE_TUNNEL: process.env.VSCODE_TUNNEL,
    HOST: process.env.HOST,
    PORT: process.env.PORT,
    PUBLIC_URL: process.env.PUBLIC_URL,
    REACT_APP_DEV_TUNNEL: process.env.REACT_APP_DEV_TUNNEL
  });

  let target;

  if (isDevTunnel) {
    // In dev tunnel environments, always proxy to localhost backend
    target = 'http://localhost:5000';
    console.log('🌐 Dev tunnel detected - proxying to localhost backend');
  } else {
    // Determine backend target based on HTTPS availability for local development
    const useHttps = process.env.REACT_APP_USE_HTTPS === 'true';
    const hostname = process.env.REACT_APP_LOCAL_NETWORK_IP || getLocalNetworkIP();

    // Extract port from REACT_APP_API_URL if available, otherwise use defaults
    let httpsPort = 5444;
    let httpPort = 5000;

    if (process.env.REACT_APP_API_URL) {
      const apiUrl = new URL(process.env.REACT_APP_API_URL);
      if (apiUrl.protocol === 'https:') {
        httpsPort = parseInt(apiUrl.port) || 5444;
      } else {
        httpPort = parseInt(apiUrl.port) || 5000;
      }
    }

    target = useHttps ? `https://${hostname}:${httpsPort}` : `http://${hostname}:${httpPort}`;
  }

  console.log(`🔄 Proxy target: ${target}`);
  console.log(`🔗 Proxy configuration: ${target}`);

  // Proxy API requests to the backend server with fallback
  app.use('/api', createProxyMiddleware({
    target: target,
    changeOrigin: true,
    secure: false, // Allow self-signed certificates in development
    logLevel: 'warn', // Reduce log verbosity
    onError: (err, req, res) => {
      console.error('❌ Proxy error:', err.message);

      // Provide mock response when backend is not available
      if (err.code === 'ECONNREFUSED') {
        console.warn('⚠️  Backend not available, providing mock response');

        // Mock responses for common endpoints
        if (req.url.includes('/health')) {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            status: 'OK',
            message: 'Mock backend response',
            timestamp: new Date().toISOString()
          }));
        } else if (req.url.includes('/auth/login')) {
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            success: true,
            message: 'Mock login successful',
            user: { id: 1, username: 'demo', role: 'admin' },
            token: 'mock-token-123'
          }));
        } else {
          res.writeHead(503, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            error: 'Backend Unavailable',
            message: 'Backend server is not running. Using mock mode.',
            mockMode: true
          }));
        }
      }
    },
    onProxyReq: (proxyReq, req, res) => {
      // Add debug logging for proxy requests
      if (process.env.REACT_APP_DEBUG_MODE === 'true') {
        console.log(`🔄 Proxying: ${req.method} ${req.url} -> ${target}${req.url}`);
      }
    }
  }));

  // Enhanced WebSocket proxy configuration
  console.log('🔌 Setting up WebSocket proxy...');

  // Application WebSocket proxy (for our app's real-time features)
  app.use('/ws', createProxyMiddleware({
    target: target.replace('https:', 'http:'), // Force HTTP for WebSocket backend connection
    ws: true,
    changeOrigin: true,
    secure: false,
    logLevel: 'warn', // Reduce verbosity
    timeout: 30000, // 30 second timeout
    proxyTimeout: 30000,
    onError: (err, req, res) => {
      console.error('❌ WebSocket proxy error:', {
        message: err.message,
        code: err.code,
        target: target,
        url: req.url,
        headers: req.headers
      });
    },
    onProxyReqWs: (proxyReq, req, socket, options, head) => {
      console.log('🔄 Proxying WebSocket connection:', {
        url: req.url,
        target: options.target,
        headers: req.headers
      });

      // Add custom headers for dev tunnel compatibility
      proxyReq.setHeader('X-Forwarded-Proto', 'https');
      proxyReq.setHeader('X-Forwarded-Host', req.headers.host);
    },
    onOpen: (proxySocket) => {
      console.log('✅ WebSocket proxy connection opened');
    },
    onClose: (res, socket, head) => {
      console.log('🔌 WebSocket proxy connection closed');
    }
  }));

  // Webpack Dev Server WebSocket proxy (for hot reload)
  if (isDevTunnel) {
    console.log('🔥 Setting up webpack-dev-server WebSocket proxy for hot reload...');
    app.use('/sockjs-node', createProxyMiddleware({
      target: 'http://localhost:3000',
      ws: true,
      changeOrigin: true,
      secure: false,
      logLevel: 'warn',
      onError: (err) => {
        console.error('❌ Webpack WebSocket proxy error:', err.message);
      }
    }));
  }

  // Handle favicon requests to prevent proxy errors
  app.use('/favicon.ico', (req, res) => {
    res.status(204).end();
  });
};
