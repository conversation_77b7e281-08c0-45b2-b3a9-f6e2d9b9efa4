@echo off
REM Windows batch file to run Ubuntu 24.04 container
REM For Docker Desktop on Windows

echo Starting Ubuntu 24.04 container for script testing...
echo.

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo Error: Docker Desktop is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM Pull Ubuntu 24.04 image
echo Pulling Ubuntu 24.04 image...
docker pull ubuntu:24.04

REM Remove existing container if it exists
docker rm ubuntu-test-24 >nul 2>&1

REM Run new container
echo Starting container with workspace mounted...
docker run -it --name ubuntu-test-24 ^
  -v %cd%:/workspace ^
  -e DEBIAN_FRONTEND=noninteractive ^
  ubuntu:24.04 bash -c "
    echo === Ubuntu 24.04 Container Ready ===
    echo.
    echo Container Configuration:
    echo - Image: Ubuntu 24.04
    echo - Workspace: /workspace (mounted from %cd%)
    echo - User: root
    echo.
    echo To install basic tools:
    echo   apt update ^&^& apt install -y bash curl wget vim
    echo.
    echo To test your scripts:
    echo   cd /workspace
    echo   chmod +x your-script.sh
    echo   ./your-script.sh
    echo.
    bash
"

echo.
echo Container stopped.
echo To restart the same container: docker start -ai ubuntu-test-24
echo To remove container: docker rm ubuntu-test-24
pause