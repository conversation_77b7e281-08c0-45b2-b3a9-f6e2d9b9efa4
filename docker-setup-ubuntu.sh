#!/bin/bash
#
# Quick Ubuntu Setup Script for Docker Container
# Run this inside the Ubuntu container to set up testing environment
#

echo "========================================="
echo "Setting up Ubuntu Testing Environment"
echo "========================================="

# Update package lists
echo "📦 Updating package lists..."
apt update

# Install essential tools
echo "🔧 Installing essential tools..."
apt install -y jq curl wget git

# Install Node.js and npm
echo "🟢 Installing Node.js and npm..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt install -y nodejs

# Install yq for YAML processing
echo "📄 Installing yq for YAML processing..."
wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64
chmod +x /usr/local/bin/yq

# Make scripts executable
echo "🔐 Making scripts executable..."
chmod +x test-deployment-wsl.sh 2>/dev/null || echo "test-deployment-wsl.sh not found"
chmod +x run-wsl-tests.sh 2>/dev/null || echo "run-wsl-tests.sh not found"
chmod +x deploy-hauling-qr-ubuntu.sh 2>/dev/null || echo "deploy-hauling-qr-ubuntu.sh not found"

# Verify installations
echo "========================================="
echo "✅ Installation Verification:"
echo "- Node.js: $(node --version 2>/dev/null || echo 'Not installed')"
echo "- NPM: $(npm --version 2>/dev/null || echo 'Not installed')"
echo "- jq: $(jq --version 2>/dev/null || echo 'Not installed')"
echo "- yq: $(yq --version 2>/dev/null || echo 'Not installed')"
echo "- curl: $(curl --version 2>/dev/null | head -1 || echo 'Not installed')"
echo "- wget: $(wget --version 2>/dev/null | head -1 || echo 'Not installed')"
echo "- git: $(git --version 2>/dev/null || echo 'Not installed')"
echo "========================================="

echo "🎉 Ubuntu environment setup completed!"
echo ""
echo "📋 Available commands:"
echo "  ./run-wsl-tests.sh                    # Run all tests"
echo "  ./test-deployment-wsl.sh --test-all  # Run comprehensive tests"
echo "  ./deploy-hauling-qr-ubuntu.sh --help # View deployment options"
echo ""
echo "🔍 To test specific functionality:"
echo "  ./test-deployment-wsl.sh --test-config     # Test configuration parsing"
echo "  ./test-deployment-wsl.sh --test-detection  # Test component detection"
echo "  ./test-deployment-wsl.sh --test-backup     # Test backup functionality"
echo ""