#!/bin/bash

# Define logging functions that are missing
log_step() {
    echo "[STEP] $1"
}

log_debug() {
    echo "[DEBUG] $1"
}

log_info() {
    echo "[INFO] $1"
}

log_success() {
    echo "[SUCCESS] $1"
}

log_warning() {
    echo "[WARNING] $1"
}

log_error() {
    echo "[ERROR] $1"
}

log_section() {
    echo "[SECTION] $1"
}

# Source the simple state management functions
source ./simple-state-management.sh

# Set up test environment
export DOMAIN_NAME="test.example.com"
export ENV_MODE="test"
export SSL_MODE="cloudflare"
export VERSION="1.0.0"

echo "========================================="
echo "Testing Deployment State Management"
echo "========================================="

# Install jq if not present
if ! command -v jq &> /dev/null; then
    echo "Installing jq..."
    apt-get update -qq && apt-get install -y jq
fi

# Test 1: Initialize state management
echo "Test 1: Initializing state management"
init_deployment_state_management
if [[ -f "$STATE_FILE" ]]; then
    echo "✅ State file created successfully"
else
    echo "❌ Failed to create state file"
fi

# Test 2: Set deployment phase
echo -e "\nTest 2: Setting deployment phase"
set_deployment_phase "system_preparation" "updating_packages"
if grep -q "system_preparation" "$STATE_FILE"; then
    echo "✅ Phase set successfully"
else
    echo "❌ Failed to set phase"
fi

# Test 3: Create checkpoint
echo -e "\nTest 3: Creating checkpoint"
create_deployment_checkpoint "after_system_preparation"
if [[ -n "$LAST_CHECKPOINT" && -f "$CHECKPOINT_DIR/$LAST_CHECKPOINT.json" ]]; then
    echo "✅ Checkpoint created successfully"
else
    echo "❌ Failed to create checkpoint"
fi

# Test 4: Complete deployment phase
echo -e "\nTest 4: Completing deployment phase"
complete_deployment_phase "system_preparation" "true"
if grep -q "system_preparation" "$STATE_FILE" && grep -q "last_completed_phase" "$STATE_FILE"; then
    echo "✅ Phase completed successfully"
else
    echo "❌ Failed to complete phase"
fi

# Test 5: Validate state integrity
echo -e "\nTest 5: Validating state integrity"
validate_deployment_state
if [[ $? -eq 0 ]]; then
    echo "✅ State validation passed"
else
    echo "❌ State validation failed"
fi

# Test 6: Generate state report
echo -e "\nTest 6: Generating state report"
report_file=$(generate_deployment_state_report)
if [[ -f "$report_file" ]]; then
    echo "✅ Report generated successfully: $report_file"
else
    echo "❌ Failed to generate report"
fi

echo -e "\n========================================="
echo "Test Summary"
echo "========================================="
echo "State file: $STATE_FILE"
echo "Checkpoint directory: $CHECKPOINT_DIR"
echo "Last checkpoint: $LAST_CHECKPOINT"
echo -e "\nState file contents:"
cat "$STATE_FILE" | head -20
echo "..."

echo -e "\nCheckpoint file contents:"
cat "$CHECKPOINT_DIR/$LAST_CHECKPOINT.json" | head -10
echo "..."