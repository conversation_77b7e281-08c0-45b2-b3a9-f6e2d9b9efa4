#!/bin/bash
#
# Rollback Implementation for Ubuntu Deployment Script
#
# This file contains the rollback functions that need to be integrated
# into the main deploy-hauling-qr-ubuntu.sh script to complete task 9.3
#

# ==============================
# Rollback Functions
# ==============================

# Function to restore configuration from backup
restore_from_backup() {
    local backup_id="$1"
    local backup_dir="$BACKUP_BASE_DIR/$backup_id"
    
    log_step "Restoring configuration from backup: $backup_id"
    current_step="rollback_restore"
    
    # Validate backup exists and is complete
    if [[ ! -d "$backup_dir" ]]; then
        log_error "Backup directory not found: $backup_dir"
        return 1
    fi
    
    if [[ ! -f "$backup_dir/backup-metadata.json" ]]; then
        log_error "Backup metadata not found: $backup_dir/backup-metadata.json"
        return 1
    fi
    
    # Verify backup integrity before restoration
    if ! verify_backup_integrity "$backup_id"; then
        log_error "Backup integrity verification failed for: $backup_id"
        return 1
    fi
    
    log_info "Backup integrity verified, proceeding with restoration"
    
    # Create pre-rollback backup for safety
    local pre_rollback_backup_id="pre-rollback-$(date +%Y%m%d_%H%M%S)"
    log_info "Creating pre-rollback backup: $pre_rollback_backup_id"
    
    if ! create_configuration_backup "$pre_rollback_backup_id"; then
        log_warning "Failed to create pre-rollback backup, continuing anyway"
    fi
    
    # Restore files from backup using metadata
    local restored_count=0
    local failed_count=0
    
    # Read backup metadata and restore each file
    while IFS= read -r backup_entry; do
        local source_path=$(echo "$backup_entry" | jq -r '.source')
        local backup_path=$(echo "$backup_entry" | jq -r '.backup_path')
        local category=$(echo "$backup_entry" | jq -r '.category')
        local permissions=$(echo "$backup_entry" | jq -r '.permissions')
        local owner=$(echo "$backup_entry" | jq -r '.owner')
        
        if [[ "$source_path" == "null" || "$backup_path" == "null" ]]; then
            continue
        fi
        
        log_info "Restoring: $source_path"
        
        # Create directory if it doesn't exist
        local source_dir=$(dirname "$source_path")
        if [[ ! -d "$source_dir" ]]; then
            mkdir -p "$source_dir"
        fi
        
        # Restore the file
        if cp "$backup_path" "$source_path"; then
            # Restore permissions if available
            if [[ "$permissions" != "null" && "$permissions" != "unknown" ]]; then
                chmod "$permissions" "$source_path" 2>/dev/null || log_warning "Failed to restore permissions for $source_path"
            fi
            
            # Restore ownership if available and we have permission
            if [[ "$owner" != "null" && "$owner" != "unknown" && $EUID -eq 0 ]]; then
                chown "$owner" "$source_path" 2>/dev/null || log_warning "Failed to restore ownership for $source_path"
            fi
            
            log_info "✓ Restored: $source_path"
            ((restored_count++))
        else
            log_error "✗ Failed to restore: $source_path"
            ((failed_count++))
        fi
        
    done < <(jq -c '.backup_files[]' "$backup_dir/backup-metadata.json")
    
    log_info "Restoration completed: $restored_count files restored, $failed_count failed"
    
    if [[ $failed_count -gt 0 ]]; then
        log_warning "Some files failed to restore. System may be in inconsistent state."
        return 1
    fi
    
    log_success "All files restored successfully from backup: $backup_id"
    return 0
}

# Function to restore service states during rollback
restore_service_states() {
    local backup_id="$1"
    local backup_dir="$BACKUP_BASE_DIR/$backup_id"
    
    log_step "Restoring service states"
    current_step="rollback_services"
    
    # Check if service state backup exists
    local service_state_file="$backup_dir/service-states.json"
    if [[ ! -f "$service_state_file" ]]; then
        log_warning "Service state backup not found, skipping service restoration"
        return 0
    fi
    
    log_info "Restoring service states from backup"
    
    # Restore each service state
    while IFS= read -r service_entry; do
        local service_name=$(echo "$service_entry" | jq -r '.name')
        local service_state=$(echo "$service_entry" | jq -r '.state')
        local service_enabled=$(echo "$service_entry" | jq -r '.enabled')
        
        if [[ "$service_name" == "null" ]]; then
            continue
        fi
        
        log_info "Restoring service: $service_name (state: $service_state, enabled: $service_enabled)"
        
        # Restore service state
        case "$service_state" in
            "active")
                if systemctl start "$service_name" 2>/dev/null; then
                    log_info "✓ Started service: $service_name"
                else
                    log_warning "Failed to start service: $service_name"
                fi
                ;;
            "inactive")
                if systemctl stop "$service_name" 2>/dev/null; then
                    log_info "✓ Stopped service: $service_name"
                else
                    log_warning "Failed to stop service: $service_name"
                fi
                ;;
        esac
        
        # Restore enabled state
        if [[ "$service_enabled" == "enabled" ]]; then
            if systemctl enable "$service_name" 2>/dev/null; then
                log_info "✓ Enabled service: $service_name"
            else
                log_warning "Failed to enable service: $service_name"
            fi
        elif [[ "$service_enabled" == "disabled" ]]; then
            if systemctl disable "$service_name" 2>/dev/null; then
                log_info "✓ Disabled service: $service_name"
            else
                log_warning "Failed to disable service: $service_name"
            fi
        fi
        
    done < <(jq -c '.services[]' "$service_state_file" 2>/dev/null || echo '[]')
    
    log_success "Service state restoration completed"
    return 0
}

# Function to validate system state after rollback
validate_rollback_state() {
    local backup_id="$1"
    
    log_step "Validating system state after rollback"
    current_step="rollback_validation"
    
    local validation_errors=0
    
    # Test Nginx configuration if it was restored
    if [[ -f "/etc/nginx/nginx.conf" ]]; then
        log_info "Validating Nginx configuration"
        if nginx -t 2>/dev/null; then
            log_info "✓ Nginx configuration is valid"
        else
            log_error "✗ Nginx configuration validation failed"
            ((validation_errors++))
        fi
    fi
    
    # Test PostgreSQL connection if it was restored
    if command -v psql &> /dev/null && systemctl is-active postgresql &> /dev/null; then
        log_info "Validating PostgreSQL connection"
        if sudo -u postgres psql -c "SELECT 1;" &> /dev/null; then
            log_info "✓ PostgreSQL connection is working"
        else
            log_error "✗ PostgreSQL connection failed"
            ((validation_errors++))
        fi
    fi
    
    # Test application configuration if .env was restored
    if [[ -f ".env" ]]; then
        log_info "Validating application configuration"
        if grep -q "DOMAIN_NAME" .env && grep -q "DB_PASSWORD" .env; then
            log_info "✓ Application configuration appears valid"
        else
            log_error "✗ Application configuration validation failed"
            ((validation_errors++))
        fi
    fi
    
    # Test SSL certificates if they were restored
    if [[ -d "/etc/nginx/ssl" ]]; then
        log_info "Validating SSL certificates"
        local cert_count=$(find /etc/nginx/ssl -name "*.crt" -o -name "*.pem" | wc -l)
        if [[ $cert_count -gt 0 ]]; then
            log_info "✓ SSL certificates found ($cert_count files)"
        else
            log_warning "No SSL certificates found after rollback"
        fi
    fi
    
    if [[ $validation_errors -eq 0 ]]; then
        log_success "System state validation passed"
        return 0
    else
        log_error "System state validation failed with $validation_errors errors"
        return 1
    fi
}

# Main rollback function
rollback_deployment() {
    local backup_id="$1"
    local force_rollback="${2:-false}"
    
    log_section "Deployment Rollback"
    current_step="rollback_deployment"
    
    if [[ -z "$backup_id" ]]; then
        log_error "Backup ID is required for rollback"
        echo "Available backups:"
        list_available_backups
        return 1
    fi
    
    # Confirm rollback unless forced
    if [[ "$force_rollback" != "true" && "$INTERACTIVE" == "true" ]]; then
        echo
        echo "WARNING: This will restore your system to a previous state."
        echo "Backup ID: $backup_id"
        echo "Current configurations will be overwritten."
        echo
        read -p "Are you sure you want to proceed with rollback? (yes/no): " confirm_rollback
        
        if [[ "$confirm_rollback" != "yes" ]]; then
            log_info "Rollback cancelled by user"
            return 0
        fi
    fi
    
    log_info "Starting rollback to backup: $backup_id"
    
    # Step 1: Restore configuration files
    if restore_from_backup "$backup_id"; then
        log_success "Configuration files restored successfully"
    else
        log_error "Failed to restore configuration files"
        return 1
    fi
    
    # Step 2: Restore service states
    if restore_service_states "$backup_id"; then
        log_success "Service states restored successfully"
    else
        log_warning "Service state restoration completed with warnings"
    fi
    
    # Step 3: Validate system state
    if validate_rollback_state "$backup_id"; then
        log_success "System state validation passed"
    else
        log_error "System state validation failed"
        if [[ "$INTERACTIVE" == "true" ]]; then
            read -p "Continue despite validation errors? (y/n): " continue_choice
            if [[ "$continue_choice" != "y" ]]; then
                return 1
            fi
        else
            return 1
        fi
    fi
    
    # Step 4: Create rollback report
    create_rollback_report "$backup_id"
    
    log_success "Rollback completed successfully"
    log_info "System has been restored to backup: $backup_id"
    
    return 0
}

# Function to create rollback report
create_rollback_report() {
    local backup_id="$1"
    local report_file="/var/lib/hauling-deployment/rollback-report-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
========================================
Hauling QR Deployment Rollback Report
========================================
Date: $(date)
Backup ID: $backup_id
Rollback Initiated By: $(whoami)
Hostname: $(hostname)

Rollback Summary:
- Configuration files restored from backup
- Service states restored where applicable
- System state validation performed
- Rollback completed successfully

Backup Details:
$(show_backup_details "$backup_id" 2>/dev/null || echo "Backup details not available")

System Status After Rollback:
- Nginx: $(systemctl is-active nginx 2>/dev/null || echo "Not available")
- PostgreSQL: $(systemctl is-active postgresql 2>/dev/null || echo "Not available")
- UFW: $(systemctl is-active ufw 2>/dev/null || echo "Not available")

For support or issues, please refer to the deployment documentation.
========================================
EOF
    
    log_info "Rollback report created: $report_file"
}

# Function to save service states during backup (to be integrated with backup functions)
save_service_states() {
    local backup_dir="$1"
    local service_state_file="$backup_dir/service-states.json"
    
    log_debug "Saving service states to: $service_state_file"
    
    # Services to track
    local services=("nginx" "postgresql" "ufw" "fail2ban")
    
    # Create service states JSON
    echo '{"services":[]}' > "$service_state_file"
    
    for service in "${services[@]}"; do
        if systemctl list-unit-files "$service.service" &> /dev/null; then
            local state=$(systemctl is-active "$service" 2>/dev/null || echo "unknown")
            local enabled=$(systemctl is-enabled "$service" 2>/dev/null || echo "unknown")
            
            # Add service to JSON
            local temp_file=$(mktemp)
            jq ".services += [{
                \"name\": \"$service\",
                \"state\": \"$state\",
                \"enabled\": \"$enabled\",
                \"timestamp\": \"$(date)\"
            }]" "$service_state_file" > "$temp_file" && mv "$temp_file" "$service_state_file"
            
            log_debug "Saved state for $service: $state ($enabled)"
        fi
    done
    
    log_debug "Service states saved successfully"
}

# Command line argument parsing for rollback functionality
parse_rollback_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --rollback)
                ROLLBACK_MODE=true
                ROLLBACK_BACKUP_ID="$2"
                shift 2
                ;;
            --force-rollback)
                ROLLBACK_MODE=true
                ROLLBACK_BACKUP_ID="$2"
                FORCE_ROLLBACK=true
                shift 2
                ;;
            --list-backups)
                LIST_BACKUPS=true
                shift
                ;;
            --show-backup)
                SHOW_BACKUP_ID="$2"
                shift 2
                ;;
            *)
                # Pass through other arguments
                shift
                ;;
        esac
    done
}

# Main rollback execution logic (to be integrated into main script)
execute_rollback_mode() {
    if [[ "$LIST_BACKUPS" == true ]]; then
        list_available_backups
        exit 0
    fi
    
    if [[ -n "$SHOW_BACKUP_ID" ]]; then
        show_backup_details "$SHOW_BACKUP_ID"
        exit 0
    fi
    
    if [[ "$ROLLBACK_MODE" == true ]]; then
        if [[ -z "$ROLLBACK_BACKUP_ID" ]]; then
            log_error "Backup ID is required for rollback"
            echo "Use --list-backups to see available backups"
            exit 1
        fi
        
        rollback_deployment "$ROLLBACK_BACKUP_ID" "$FORCE_ROLLBACK"
        exit $?
    fi
}

# Integration instructions:
# 1. Add these functions to the main deploy-hauling-qr-ubuntu.sh script
# 2. Add rollback argument parsing to the main argument parsing section
# 3. Add execute_rollback_mode() call before main deployment logic
# 4. Integrate save_service_states() into the backup creation functions
# 5. Update help text to include rollback options

echo "Rollback implementation functions loaded successfully"
echo "These functions need to be integrated into deploy-hauling-qr-ubuntu.sh"