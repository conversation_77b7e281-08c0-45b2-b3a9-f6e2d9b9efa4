@echo off
REM Simple Docker test script

echo Testing Docker setup...
echo.

REM Check Docker
docker --version
if errorlevel 1 (
    echo Docker not found - install Docker Desktop
    pause
    exit /b 1
)

REM Check if running
docker info >nul 2>&1
if errorlevel 1 (
    echo Docker not running - start Docker Desktop
    pause
    exit /b 1
)

echo Docker is working ✓
echo.

REM Test simple container
echo Testing Ubuntu container...
docker run --rm ubuntu:24.04 echo "Ubuntu 24.04 is working!"

echo.
echo Docker test complete!
echo Now run: TROUBLESHOOT-DOCKER.cmd to start Ubuntu
pause