# Hauling QR Trip Management System Deployment Guide for Ubuntu 24.04

This guide provides comprehensive instructions for deploying the Hauling QR Trip Management System on Ubuntu 24.04 VPS servers using the advanced automated deployment script.

## 🚀 Deployment Script Features

The deployment script (v1.0.0) includes advanced capabilities:

- **Multi-format Configuration**: Shell (.conf), JSON (.json), and YAML (.yaml) support
- **Cloudflare Integration**: Optimized for truckhaul.top with Full SSL mode
- **Advanced Security**: UFW firewall, Fail2Ban, secure permissions, strong password generation
- **Comprehensive Monitoring**: Health checks, automated recovery, system metrics
- **Robust Error Handling**: Context-specific errors, recovery suggestions, detailed logging
- **Idempotent Operations**: Safe to run multiple times without breaking existing installations
- **CI/CD Ready**: Non-interactive mode with JSON output for automation

## Prerequisites

- **Server**: Clean Ubuntu 24.04 VPS with root access
- **Resources**: Minimum 2GB RAM, 1 CPU core, 20GB disk space
- **Network**: Domain name pointing to server IP (for SSL)
- **Access**: SSH access to the server

## 🛠️ Deployment Options

The deployment script supports multiple deployment modes:

### 1. Interactive Mode
The script prompts for all required configuration parameters with intelligent defaults.

### 2. Configuration File Mode
Supports multiple configuration formats:
- **Shell format** (.conf, .env, .sh): Traditional key=value pairs
- **JSON format** (.json): Structured configuration with validation
- **YAML format** (.yaml, .yml): Human-readable structured configuration

### 3. Command-line Parameters
Basic parameters can be provided via command-line arguments for quick deployments.

### 4. Non-interactive Mode
Designed for CI/CD pipelines with:
- No user prompts required
- JSON output for machine parsing
- Detailed exit codes for automation
- Quiet mode for minimal output

## Quick Start

For a quick deployment with interactive prompts:

1. SSH into your Ubuntu 24.04 server as root
2. Download the deployment script:
   ```bash
   wget https://raw.githubusercontent.com/your-org/hauling-qr-trip-system/main/deploy-hauling-qr-ubuntu.sh
   ```
3. Make the script executable:
   ```bash
   chmod +x deploy-hauling-qr-ubuntu.sh
   ```
4. Run the script in interactive mode:
   ```bash
   ./deploy-hauling-qr-ubuntu.sh
   ```
5. Follow the prompts to configure your deployment

## Using a Configuration File

For automated or repeatable deployments, you can use a configuration file:

1. Download the configuration template:
   ```bash
   wget https://raw.githubusercontent.com/your-org/hauling-qr-trip-system/main/deployment-config-template.conf
   ```
2. Copy and modify the template:
   ```bash
   cp deployment-config-template.conf deployment-config.conf
   nano deployment-config.conf
   ```
3. Run the deployment script with your configuration file:
   ```bash
   ./deploy-hauling-qr-ubuntu.sh --config deployment-config.conf
   ```

## Command-line Parameters

You can also provide basic parameters via command-line arguments:

```bash
./deploy-hauling-qr-ubuntu.sh --domain yourdomain.com --env production
```

**Available command-line options:**

- `-c, --config FILE`: Path to configuration file (.conf, .json, .yaml)
- `-d, --domain DOMAIN`: Domain name for the application
- `-e, --env MODE`: Environment mode (production, staging, development)
- `-n, --non-interactive`: Run in non-interactive mode (requires config file)
- `-q, --quiet`: Minimal output for CI/CD environments
- `--dry-run`: Validate configuration without making changes
- `--log-level LEVEL`: Set log level (debug, info, warning, error)
- `--json-output`: Output logs in JSON format for machine parsing
- `--cicd-mode`: Enable CI/CD friendly mode with structured output
- `--output-file FILE`: Save structured output to specified file
- `--output-format FORMAT`: Output format for reports (text, json, yaml)
- `--progress-indicators BOOL`: Enable/disable progress indicators (true/false)
- `--exit-codes`: Show available exit codes and their meanings
- `-h, --help`: Show detailed help message

**Advanced Examples:**

```bash
# Production deployment with domain
./deploy-hauling-qr-ubuntu.sh --domain truckhaul.top --env production

# CI/CD deployment with JSON output
./deploy-hauling-qr-ubuntu.sh --config deploy.json --non-interactive --json-output

# Full CI/CD mode with structured output
./deploy-hauling-qr-ubuntu.sh --config config.yaml --cicd-mode --output-format json --output-file deployment.json

# Quiet CI/CD deployment
./deploy-hauling-qr-ubuntu.sh --config config.yaml --cicd-mode --quiet --progress-indicators false

# Debug deployment with verbose logging
./deploy-hauling-qr-ubuntu.sh --config config.yaml --log-level debug

# Validate configuration without deployment
./deploy-hauling-qr-ubuntu.sh --config config.conf --dry-run

# Show available exit codes for CI/CD integration
./deploy-hauling-qr-ubuntu.sh --exit-codes
```

## 🔒 SSL Configuration Options

The deployment script supports multiple SSL configuration modes:

### 1. Cloudflare Full SSL (Default)
- **Optimized for truckhaul.top domain**
- Self-signed certificates for server-to-Cloudflare communication
- Cloudflare handles public SSL termination
- Automatic IP detection for proper client logging
- Security headers optimized for Cloudflare

### 2. Let's Encrypt
- Automatic SSL certificate generation and renewal
- Requires domain DNS pointing to server IP
- Email required for Let's Encrypt notifications
- Automatic certificate renewal via cron

### 3. Custom Certificates
- Use your own SSL certificates from any CA
- Manual certificate management
- Full control over certificate configuration

### 4. No SSL (Development Only)
- HTTP-only deployment
- Not recommended for production use

### Let's Encrypt Configuration

To use Let's Encrypt, ensure your domain is properly configured with DNS and pointing to your server's IP address before running the deployment script. You will need to provide an email address for Let's Encrypt notifications.

### Cloudflare Configuration

To use Cloudflare, you need to:

1. Sign up for a Cloudflare account
2. Add your domain to Cloudflare
3. Update your domain's nameservers to use Cloudflare's nameservers
4. Set SSL/TLS encryption mode to "Flexible" in Cloudflare dashboard

### Custom Certificates

To use custom certificates, you need to:

1. Obtain SSL certificates from a certificate authority
2. Place the certificate and key files on your server
3. Provide the paths to these files in the configuration

## 📊 Implementation Status

### ✅ Completed Features (Ready for Production)

- **Core Deployment Infrastructure**: Full system setup with Node.js, PostgreSQL, Nginx
- **Cloudflare Integration**: Complete SSL/TLS setup for truckhaul.top domain
- **Security Hardening**: UFW firewall, Fail2Ban, secure file permissions
- **Configuration Management**: Multi-format support (.conf, .json, .yaml)
- **Advanced Logging**: Structured logs with JSON output and rotation
- **Error Handling**: Context-specific error messages and recovery mechanisms
- **Health Monitoring**: Comprehensive system checks and automated recovery
- **Database Setup**: Complete schema deployment and optimization
- **Environment Configuration**: Production-ready environment variables
- **Nginx Optimization**: Performance tuning, caching, and security headers
- **Backup System**: Automated database backups with retention policies

### ✅ Recently Completed Features

- **CI/CD Integration**: JSON output modes, structured exit codes, and CI/CD friendly modes (✅ Complete)

### ✅ Recently Completed Features

- **Component Detection and Skip Logic**: Smart detection of already installed components (Node.js, PostgreSQL, Nginx, PM2) with version checking and intelligent skip logic for redundant installations
- **Configuration Backup System**: Automated backup of existing configuration files before modification with timestamp-based versioning

### ✅ Recently Completed Features

- **Rollback Functionality**: Complete implementation of restore functions and rollback command with comprehensive backup system, metadata tracking, and service state restoration
- **Deployment State Management**: Enhanced checkpoint system with state persistence, recovery from interruption capability, and command-line options for state management
- **Performance and Security Validation**: Comprehensive testing suite for different VPS configurations and security validation (100% complete)
- **Final Script Validation**: Complete validation of all script functionality with fixes for missing functions

### 📋 Implementation Status

All tasks have been completed and the deployment system is fully production-ready:

1. **Idempotency and safety features**
   - ✅ Component detection for already installed services (COMPLETED)
   - ✅ Configuration backups before making changes (COMPLETED)
   - ✅ Rollback functionality for failed deployments (COMPLETED)
   - ✅ Deployment state management with checkpoint system (COMPLETED)

2. **Testing and validation**
   - ✅ Comprehensive test suite in Docker environment (COMPLETED)
   - ✅ Extended test scripts for component detection and backup functionality (COMPLETED)
   - ✅ Created test report templates and documentation (COMPLETED)
   - ✅ Performance and security validation suite (COMPLETED)

## Post-Deployment Steps

After the deployment is complete, you should:

1. Access your application at https://yourdomain.com
2. Log in with the admin credentials provided during deployment
3. Complete the initial system setup
4. Set up additional users as needed
5. Configure any additional application-specific settings

## Troubleshooting

If you encounter issues during deployment:

1. Check the deployment log at `/var/log/hauling-deployment.log`
2. Check the error report at `/tmp/hauling-deployment-error.log` if an error occurred
3. Check the application logs at `/var/www/hauling-qr-system/server/logs/`
4. Check the Nginx logs at `/var/log/nginx/`
5. Check the PM2 logs with `sudo -u hauling_app pm2 logs`

Common issues and solutions:

- **Database connection errors**: Check PostgreSQL is running with `systemctl status postgresql`
- **Nginx configuration errors**: Check Nginx syntax with `nginx -t`
- **SSL certificate errors**: Ensure your domain is properly configured with DNS
- **Application startup errors**: Check PM2 logs for JavaScript errors

## Security Considerations

The deployment script implements several security best practices by default:

1. Firewall (UFW) with restrictive rules
2. Fail2Ban for brute force protection
3. Secure file permissions
4. HTTPS with modern TLS protocols (when SSL is enabled)
5. Security headers in Nginx configuration
6. Rate limiting for sensitive endpoints
7. Dedicated application user instead of running as root

## Maintenance

### Database Backups

Database backups are configured to run daily at 2 AM by default. Backups are stored in `/var/backups/hauling-qr-system/` and are retained for 7 days by default.

To manually trigger a backup:

```bash
sudo /usr/local/bin/backup-hauling-db.sh
```

### Health Checks

Health checks are configured to run every 5 minutes by default. If the application becomes unresponsive, the health check will attempt to restart it automatically.

### Log Rotation

Log rotation is configured for application logs to prevent disk space issues. Logs are rotated daily and compressed after 1 day. Old logs are removed after 14 days.

### Updating the Application

To update the application to a new version:

1. SSH into your server
2. Navigate to the application directory:
   ```bash
   cd /var/www/hauling-qr-system
   ```
3. Pull the latest changes:
   ```bash
   sudo -u hauling_app git pull
   ```
4. Install dependencies and rebuild:
   ```bash
   sudo -u hauling_app npm install
   cd client
   sudo -u hauling_app npm install
   sudo -u hauling_app npm run build
   cd ..
   ```
5. Restart the application:
   ```bash
   sudo -u hauling_app pm2 restart all
   ```

## Support

If you encounter any issues with the deployment script or need assistance, please:

1. Check the comprehensive [Auto Deployment Ubuntu Guide](docs/AUTO_DEPLOYMENT_UBUNTU_GUIDE.md)
2. Review the deployment logs for error messages
3. Check the documentation in the `/docs` directory
4. Contact the system administrator or development team for support

## 🤖 CI/CD Integration

The deployment script includes comprehensive CI/CD integration features for automated deployment pipelines:

### CI/CD Mode Features

- **Structured Output**: JSON and YAML output formats for machine parsing
- **Exit Codes**: Detailed exit codes for different failure scenarios
- **Quiet Mode**: Minimal console output for clean CI/CD logs
- **Progress Indicators**: Optional progress indicators compatible with CI/CD systems
- **Output Files**: Save structured deployment results to files

### Exit Codes

The script provides detailed exit codes for automation:

- `0` - Success: Deployment completed successfully
- `1` - General error: Unspecified error occurred
- `2` - Configuration error: Invalid or missing configuration
- `3` - Network error: Connectivity issues or download failures
- `4` - Permission error: Insufficient permissions
- `5` - Database error: Database setup or connection issues
- `6` - SSL error: Certificate or SSL configuration issues
- `7` - Service error: System service startup failures

### GitHub Actions Example

```yaml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Deploy Application
        run: |
          ./deploy-hauling-qr-ubuntu.sh \
            --config production-config.json \
            --cicd-mode \
            --output-format json \
            --output-file deployment-result.json
        
      - name: Handle Deployment Result
        run: |
          case ${{ steps.deploy.outputs.exit-code }} in
            2) echo "Configuration error - check your config file" ;;
            3) echo "Network error - check connectivity" ;;
            5) echo "Database error - check database configuration" ;;
            *) echo "Deployment completed with exit code ${{ steps.deploy.outputs.exit-code }}" ;;
          esac
```

### CI/CD Usage Examples

```bash
# Basic CI/CD deployment
./deploy-hauling-qr-ubuntu.sh --config config.json --cicd-mode

# Full CI/CD with structured output
./deploy-hauling-qr-ubuntu.sh \
  --config config.yaml \
  --cicd-mode \
  --output-format json \
  --output-file deployment.json \
  --quiet

# Validate configuration in CI/CD
./deploy-hauling-qr-ubuntu.sh --config config.conf --dry-run --json-output
```

## 🔄 Recent Updates

**January 2025**: The auto-deployment system has been significantly enhanced with:

### ✅ Completed Enhancements
- **Multi-format Configuration**: Support for shell (.conf), JSON (.json), and YAML (.yaml) formats
- **Advanced Cloudflare Integration**: Optimized for truckhaul.top domain with Full SSL mode
- **Comprehensive Security**: UFW firewall, Fail2Ban, secure permissions, strong password generation
- **Robust Error Handling**: Context-specific error messages with recovery suggestions
- **Advanced Logging**: Structured logs with JSON output and rotation
- **Health Monitoring**: Automated system checks and recovery mechanisms
- **Performance Optimization**: Nginx caching, compression, and rate limiting
- **CI/CD Integration**: Complete JSON output modes, structured exit codes, and automation support
- **Component Detection**: Smart detection of installed components with version checking and skip logic

### ✅ Recently Completed
- **Rollback Functionality**: Complete implementation of restore functions and rollback command
- **Deployment State Management**: Enhanced checkpoint system with recovery capabilities
- **Testing Suite**: Comprehensive test suite with Docker environment validation
- **Performance and Security Validation**: Complete validation suite for system verification

The deployment system is **fully production-ready** with all planned features implemented and thoroughly tested. Component detection, configuration backups, rollback functionality, and deployment state management are all complete and verified through comprehensive testing. The final validation has been completed with all identified issues fixed and documented in [Final Deployment Script Validation](../FINAL_DEPLOYMENT_SCRIPT_VALIDATION.md).