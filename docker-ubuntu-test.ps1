# Docker Ubuntu Testing Environment Setup
# Run this PowerShell script to set up Ubuntu container for testing

Write-Host "=========================================" -ForegroundColor Cyan
Write-Host "Docker Ubuntu Testing Environment Setup" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Check if Docker is running
Write-Host "Checking Docker status..." -ForegroundColor Yellow
try {
    docker info | Out-Null
    Write-Host "✓ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker is not running. Please start Docker Desktop." -ForegroundColor Red
    exit 1
}

# Get current directory (project root)
$ProjectPath = Get-Location
Write-Host "Project directory: $ProjectPath" -ForegroundColor Blue

# Create and run Ubuntu container with project mounted
Write-Host "Creating Ubuntu 24.04 container with project mounted..." -ForegroundColor Yellow

$ContainerName = "hauling-ubuntu-test"

# Remove existing container if it exists
docker rm -f $ContainerName 2>$null

# Run Ubuntu container with project directory mounted
Write-Host "Starting Ubuntu container..." -ForegroundColor Yellow
Write-Host "Container name: $ContainerName" -ForegroundColor Blue
Write-Host "Mounted path: /workspace" -ForegroundColor Blue

docker run -it --name $ContainerName `
    -v "${ProjectPath}:/workspace" `
    -w /workspace `
    ubuntu:24.04 bash -c "
        echo '========================================='
        echo 'Ubuntu 24.04 Container Ready!'
        echo '========================================='
        echo 'Project files mounted at: /workspace'
        echo 'Current directory: \$(pwd)'
        echo ''
        echo 'Available files:'
        ls -la | head -10
        echo ''
        echo 'To install dependencies, run:'
        echo '  apt update && apt install -y jq curl wget git nodejs npm'
        echo ''
        echo 'To test deployment scripts, run:'
        echo '  chmod +x test-deployment-wsl.sh'
        echo '  ./test-deployment-wsl.sh --test-all'
        echo ''
        echo 'Type \"exit\" to leave the container'
        echo '========================================='
        bash
    "

Write-Host "Container session ended." -ForegroundColor Green
Write-Host "To restart the container, run: docker start -i $ContainerName" -ForegroundColor Blue