# WSL Testing Setup Guide for Ubuntu Deployment Script

This guide helps you set up and run comprehensive tests for the Ubuntu deployment script using Windows Subsystem for Linux (WSL).

## Prerequisites

### 1. WSL Installation
Ensure you have WSL2 with Ubuntu installed:

```powershell
# In PowerShell (as Administrator)
wsl --install -d Ubuntu
```

### 2. Required Tools in WSL
Install necessary tools in your WSL Ubuntu environment:

```bash
# Update package lists
sudo apt update

# Install required tools for testing
sudo apt install -y jq yq curl wget git

# Install Node.js and npm (for testing)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installations
node --version
npm --version
jq --version
yq --version
```

## Testing Strategy

### 1. Mock Services Approach
Since WSL doesn't require actual service installations for testing, we use mock services that simulate the behavior of real components:

- **Mock Nginx**: Simulates version checking and configuration testing
- **Mock PostgreSQL**: Simulates database version detection
- **Mock PM2**: Simulates process manager functionality
- **Mock Node.js/NPM**: Uses real installations for authentic testing

### 2. Safe Testing Environment
All tests run in isolated directories (`/tmp/hauling-deployment-tests`) to avoid affecting your system.

## Running Tests

### 1. Make the test script executable
```bash
chmod +x test-deployment-wsl.sh
```

### 2. Run specific test categories

#### Test Configuration Parsing
```bash
./test-deployment-wsl.sh --test-config --verbose
```

#### Test Component Detection
```bash
./test-deployment-wsl.sh --test-detection --mock-services
```

#### Test Backup Functionality
```bash
./test-deployment-wsl.sh --test-backup
```

#### Test Rollback Functionality
```bash
./test-deployment-wsl.sh --test-rollback
```

### 3. Run comprehensive test suite
```bash
./test-deployment-wsl.sh --test-all --mock-services --verbose
```

## Test Scenarios Covered

### Configuration Testing
- ✅ Shell configuration format (`.conf`)
- ✅ JSON configuration format (`.json`)
- ✅ YAML configuration format (`.yaml`)
- ✅ Configuration validation and error handling
- ✅ Default value handling

### Component Detection Testing
- ✅ Node.js version detection and compatibility checking
- ✅ NPM version detection and compatibility checking
- ✅ Nginx version detection (mocked)
- ✅ PostgreSQL version detection (mocked)
- ✅ PM2 version detection (mocked)
- ✅ Component status reporting

### Backup Functionality Testing
- ✅ Backup directory creation and structure
- ✅ Configuration file backup with metadata
- ✅ Backup integrity verification
- ✅ Backup cleanup and retention policies
- ✅ Backup listing and details display

### Rollback Functionality Testing
- ✅ Rollback command line option parsing
- ✅ Rollback function existence verification
- ✅ Backup restoration simulation
- ✅ Service state management during rollback

### Deployment Modes Testing
- ✅ Interactive mode simulation
- ✅ Non-interactive mode testing
- ✅ Dry-run mode validation
- ✅ CI/CD mode compatibility

## WSL-Specific Considerations

### 1. File System Permissions
WSL handles Linux file permissions within the Windows file system. The test script accounts for this by:
- Using appropriate permission checks
- Creating files with correct ownership
- Testing permission-related functionality safely

### 2. Service Management
Since WSL doesn't run systemd by default, the tests:
- Mock service status checks
- Simulate service start/stop operations
- Test service configuration without actual service management

### 3. Network Configuration
WSL networking is handled differently than native Linux:
- Tests use localhost and mock network configurations
- SSL certificate testing uses self-signed certificates
- Port availability testing is simulated

## Interpreting Test Results

### Success Indicators
- ✅ **Green [PASS]**: Test completed successfully
- ℹ️ **Blue [INFO]**: Informational message (verbose mode)
- ⚠️ **Yellow [WARN]**: Warning that doesn't affect functionality

### Failure Indicators
- ❌ **Red [FAIL]**: Test failed and needs attention
- 🔍 **Check logs**: Detailed error information in test report

### Test Report Location
After running tests, find detailed results at:
```
/tmp/hauling-deployment-tests/test-report.txt
```

## Troubleshooting Common Issues

### 1. Permission Denied Errors
```bash
# Make sure the test script is executable
chmod +x test-deployment-wsl.sh

# Ensure WSL has proper permissions
sudo chown -R $USER:$USER /tmp/hauling-deployment-tests
```

### 2. Missing Dependencies
```bash
# Install missing tools
sudo apt update
sudo apt install -y jq yq curl wget

# For YAML processing
sudo snap install yq
```

### 3. Mock Services Not Working
```bash
# Verify PATH includes mock services
echo $PATH | grep -q "/tmp/hauling-deployment-tests/mock-services/bin"

# Manually add to PATH if needed
export PATH="/tmp/hauling-deployment-tests/mock-services/bin:$PATH"
```

### 4. Configuration File Issues
```bash
# Verify configuration files are created correctly
ls -la /tmp/hauling-deployment-tests/configs/

# Check configuration file syntax
jq . /tmp/hauling-deployment-tests/configs/test-config.json
yq eval '.' /tmp/hauling-deployment-tests/configs/test-config.yaml
```

## Integration with Kiro IDE

### Running Tests from Kiro Terminal
1. Open Kiro Terminal
2. Navigate to your project directory
3. Run the WSL test script:
```bash
wsl ./test-deployment-wsl.sh --test-all --verbose
```

### Viewing Results in Kiro
- Test logs are saved in structured format
- Results can be viewed in Kiro's file explorer
- Integration with Kiro's problem detection system

## Next Steps

After running the WSL tests successfully:

1. **Validate Results**: Review the test report for any failures
2. **Fix Issues**: Address any failed tests before proceeding
3. **Extend Tests**: Add additional test scenarios as needed
4. **Production Testing**: Consider testing on actual Ubuntu VPS for final validation

## Advanced Testing Options

### Custom Configuration Testing
```bash
# Test with your own configuration
cp your-config.conf /tmp/hauling-deployment-tests/configs/
./test-deployment-wsl.sh --test-config --verbose
```

### Performance Testing
```bash
# Test with timing measurements
time ./test-deployment-wsl.sh --test-all
```

### Continuous Integration
```bash
# CI-friendly output
./test-deployment-wsl.sh --test-all --quiet > test-results.log 2>&1
echo "Exit code: $?"
```

This WSL testing approach provides comprehensive validation of the deployment script functionality without requiring actual Ubuntu server access or root privileges, making it perfect for development and testing in a Windows environment with WSL.