#!/bin/bash
#
# Quick WSL Test Runner
# 
# This script provides an easy way to run the WSL deployment tests
# with common configurations and helpful output.
#

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}WSL Deployment Testing Quick Runner${NC}"
echo -e "${BLUE}=========================================${NC}"

# Check if we're in WSL
if ! grep -q Microsoft /proc/version 2>/dev/null; then
    echo -e "${YELLOW}Warning: This doesn't appear to be running in WSL${NC}"
    echo -e "${YELLOW}For best results, run this in WSL Ubuntu${NC}"
    echo
fi

# Check if test script exists
if [[ ! -f "test-deployment-wsl.sh" ]]; then
    echo -e "${RED}Error: test-deployment-wsl.sh not found${NC}"
    echo "Please ensure you're in the correct directory"
    exit 1
fi

# Make test script executable
chmod +x test-deployment-wsl.sh

echo -e "${GREEN}Starting comprehensive WSL deployment tests...${NC}"
echo

# Run the comprehensive test suite
echo -e "${BLUE}Running all tests with mock services and verbose output...${NC}"
./test-deployment-wsl.sh --test-all --mock-services --verbose

test_exit_code=$?

echo
echo -e "${BLUE}=========================================${NC}"

if [[ $test_exit_code -eq 0 ]]; then
    echo -e "${GREEN}✅ All tests completed successfully!${NC}"
    echo
    echo -e "${BLUE}Next steps:${NC}"
    echo "1. Review the test report at: /tmp/hauling-deployment-tests/test-report.txt"
    echo "2. Check the WSL_TESTING_SETUP.md guide for more testing options"
    echo "3. Consider running individual test categories for focused testing"
    echo
    echo -e "${BLUE}Individual test commands:${NC}"
    echo "./test-deployment-wsl.sh --test-config     # Test configuration parsing"
    echo "./test-deployment-wsl.sh --test-detection  # Test component detection"
    echo "./test-deployment-wsl.sh --test-backup     # Test backup functionality"
    echo "./test-deployment-wsl.sh --test-rollback   # Test rollback functionality"
else
    echo -e "${RED}❌ Some tests failed${NC}"
    echo
    echo -e "${YELLOW}Troubleshooting steps:${NC}"
    echo "1. Check the test report: /tmp/hauling-deployment-tests/test-report.txt"
    echo "2. Review the WSL_TESTING_SETUP.md guide for common issues"
    echo "3. Run individual tests to isolate problems:"
    echo "   ./test-deployment-wsl.sh --test-config --verbose"
    echo "4. Ensure all dependencies are installed (see WSL_TESTING_SETUP.md)"
fi

echo -e "${BLUE}=========================================${NC}"

exit $test_exit_code