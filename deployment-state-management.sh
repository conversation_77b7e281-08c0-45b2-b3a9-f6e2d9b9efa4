#!/bin/bash
#
# Enhanced Deployment State Management Implementation
# 
# This file contains the enhanced state management functions that need to be integrated
# into the main deploy-hauling-qr-ubuntu.sh script to complete task 9.4
#

# ==============================
# Enhanced Deployment State Management
# ==============================

# Global state management variables
DEPLOYMENT_STATE_DIR="/var/lib/hauling-deployment/state"
CHECKPOINT_DIR="$DEPLOYMENT_STATE_DIR/checkpoints"
STATE_FILE="$DEPLOYMENT_STATE_DIR/deployment-state.json"
CHECKPOINT_INTERVAL=5  # Save checkpoint every 5 major steps

# Deployment phases and checkpoints
declare -A DEPLOYMENT_PHASES=(
    ["initialization"]="1"
    ["system_preparation"]="2"
    ["component_installation"]="3"
    ["configuration_setup"]="4"
    ["database_setup"]="5"
    ["application_deployment"]="6"
    ["ssl_configuration"]="7"
    ["service_configuration"]="8"
    ["security_setup"]="9"
    ["finalization"]="10"
)

# Current deployment state tracking
CURRENT_PHASE=""
CURRENT_STEP=""
DEPLOYMENT_START_TIME=""
LAST_CHECKPOINT=""
DEPLOYMENT_ID=""

# Initialize enhanced state management
init_deployment_state_management() {
    log_step "Initializing enhanced deployment state management"
    
    # Create state directories
    mkdir -p "$DEPLOYMENT_STATE_DIR" "$CHECKPOINT_DIR"
    
    # Generate unique deployment ID
    DEPLOYMENT_ID="deploy_$(date +%Y%m%d_%H%M%S)_$$"
    DEPLOYMENT_START_TIME=$(date +%s)
    
    # Initialize state file
    cat > "$STATE_FILE" << EOF
{
  "deployment_id": "$DEPLOYMENT_ID",
  "start_time": "$DEPLOYMENT_START_TIME",
  "start_date": "$(date)",
  "hostname": "$(hostname)",
  "user": "$(whoami)",
  "script_version": "$VERSION",
  "domain": "$DOMAIN_NAME",
  "environment": "$ENV_MODE",
  "current_phase": "initialization",
  "current_step": "init_state_management",
  "completed_phases": [],
  "completed_steps": [],
  "checkpoints": [],
  "last_checkpoint": null,
  "status": "in_progress",
  "error_count": 0,
  "warning_count": 0,
  "configuration": {
    "backup_enabled": $BACKUP_ENABLED,
    "monitoring_enabled": $MONITORING_ENABLED,
    "ssl_mode": "$SSL_MODE",
    "interactive": $INTERACTIVE
  }
}
EOF
    
    log_success "Deployment state management initialized (ID: $DEPLOYMENT_ID)"
    log_info "State directory: $DEPLOYMENT_STATE_DIR"
}

# Update current deployment phase
set_deployment_phase() {
    local phase="$1"
    local step="${2:-$phase}"
    
    CURRENT_PHASE="$phase"
    CURRENT_STEP="$step"
    
    log_debug "Setting deployment phase: $phase (step: $step)"
    
    # Update state file
    local temp_file=$(mktemp)
    jq ".current_phase = \"$phase\" | 
        .current_step = \"$step\" | 
        .last_updated = \"$(date)\" |
        .last_updated_timestamp = $(date +%s)" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    
    # Check if we should create a checkpoint
    local phase_number=${DEPLOYMENT_PHASES[$phase]:-0}
    if [[ $((phase_number % CHECKPOINT_INTERVAL)) -eq 0 ]]; then
        create_deployment_checkpoint "$phase"
    fi
}

# Mark a phase as completed
complete_deployment_phase() {
    local phase="$1"
    local success="${2:-true}"
    
    log_debug "Completing deployment phase: $phase (success: $success)"
    
    # Update state file
    local temp_file=$(mktemp)
    if [[ "$success" == "true" ]]; then
        jq ".completed_phases += [\"$phase\"] | 
            .last_completed_phase = \"$phase\" |
            .last_completed_time = \"$(date)\" |
            .last_completed_timestamp = $(date +%s)" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    else
        jq ".failed_phases += [\"$phase\"] | 
            .last_failed_phase = \"$phase\" |
            .last_failed_time = \"$(date)\" |
            .error_count += 1" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    fi
}

# Create deployment checkpoint
create_deployment_checkpoint() {
    local checkpoint_name="$1"
    local checkpoint_id="checkpoint_$(date +%Y%m%d_%H%M%S)"
    local checkpoint_file="$CHECKPOINT_DIR/$checkpoint_id.json"
    
    log_info "Creating deployment checkpoint: $checkpoint_name"
    
    # Create comprehensive checkpoint data
    cat > "$checkpoint_file" << EOF
{
  "checkpoint_id": "$checkpoint_id",
  "checkpoint_name": "$checkpoint_name",
  "deployment_id": "$DEPLOYMENT_ID",
  "timestamp": $(date +%s),
  "date": "$(date)",
  "phase": "$CURRENT_PHASE",
  "step": "$CURRENT_STEP",
  "system_state": {
    "hostname": "$(hostname)",
    "uptime": "$(uptime -p 2>/dev/null || echo 'unknown')",
    "load_average": "$(uptime | awk -F'load average:' '{print $2}' | xargs || echo 'unknown')",
    "memory_usage": "$(free -h | grep Mem | awk '{print $3 "/" $2}' || echo 'unknown')",
    "disk_usage": "$(df -h / | awk 'NR==2 {print $5}' || echo 'unknown')"
  },
  "component_status": $(get_component_status_json),
  "service_status": $(get_service_status_json),
  "configuration_files": $(get_config_files_status_json),
  "environment_variables": {
    "DOMAIN_NAME": "$DOMAIN_NAME",
    "ENV_MODE": "$ENV_MODE",
    "SSL_MODE": "$SSL_MODE",
    "BACKUP_ENABLED": "$BACKUP_ENABLED",
    "MONITORING_ENABLED": "$MONITORING_ENABLED"
  }
}
EOF
    
    # Update main state file with checkpoint reference
    local temp_file=$(mktemp)
    jq ".checkpoints += [\"$checkpoint_id\"] | 
        .last_checkpoint = \"$checkpoint_id\" |
        .last_checkpoint_time = \"$(date)\"" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    
    LAST_CHECKPOINT="$checkpoint_id"
    log_success "Checkpoint created: $checkpoint_id"
}

# Get component status as JSON
get_component_status_json() {
    local components_json='{"components":[]}'
    
    # Check each component and build JSON
    for component in nodejs npm nginx postgresql pm2; do
        local status="unknown"
        local version="unknown"
        
        case "$component" in
            "nodejs")
                if command -v node &> /dev/null; then
                    status="installed"
                    version=$(node --version 2>/dev/null | sed 's/v//')
                else
                    status="not_installed"
                fi
                ;;
            "npm")
                if command -v npm &> /dev/null; then
                    status="installed"
                    version=$(npm --version 2>/dev/null)
                else
                    status="not_installed"
                fi
                ;;
            "nginx")
                if command -v nginx &> /dev/null; then
                    status="installed"
                    version=$(nginx -v 2>&1 | grep -o '[0-9]\+\.[0-9]\+\.[0-9]\+' | head -1)
                else
                    status="not_installed"
                fi
                ;;
            "postgresql")
                if command -v psql &> /dev/null; then
                    status="installed"
                    version=$(psql --version 2>/dev/null | grep -o '[0-9]\+\.[0-9]\+' | head -1)
                else
                    status="not_installed"
                fi
                ;;
            "pm2")
                if command -v pm2 &> /dev/null; then
                    status="installed"
                    version=$(pm2 --version 2>/dev/null)
                else
                    status="not_installed"
                fi
                ;;
        esac
        
        components_json=$(echo "$components_json" | jq ".components += [{
            \"name\": \"$component\",
            \"status\": \"$status\",
            \"version\": \"$version\"
        }]")
    done
    
    echo "$components_json"
}

# Get service status as JSON
get_service_status_json() {
    local services_json='{"services":[]}'
    local services=("nginx" "postgresql" "ufw" "fail2ban")
    
    for service in "${services[@]}"; do
        local status="unknown"
        local enabled="unknown"
        
        if systemctl list-unit-files "$service.service" &> /dev/null; then
            status=$(systemctl is-active "$service" 2>/dev/null || echo "inactive")
            enabled=$(systemctl is-enabled "$service" 2>/dev/null || echo "disabled")
        fi
        
        services_json=$(echo "$services_json" | jq ".services += [{
            \"name\": \"$service\",
            \"status\": \"$status\",
            \"enabled\": \"$enabled\"
        }]")
    done
    
    echo "$services_json"
}

# Get configuration files status as JSON
get_config_files_status_json() {
    local config_files_json='{"config_files":[]}'
    local config_files=(
        "/etc/nginx/nginx.conf"
        "/etc/nginx/sites-available/hauling-qr-system"
        ".env"
        "/etc/postgresql/*/main/postgresql.conf"
        "/etc/ufw/ufw.conf"
    )
    
    for config_file in "${config_files[@]}"; do
        local exists="false"
        local size="0"
        local modified="unknown"
        
        # Handle glob patterns
        if [[ "$config_file" == *"*"* ]]; then
            local expanded_files=($(ls $config_file 2>/dev/null))
            if [[ ${#expanded_files[@]} -gt 0 ]]; then
                config_file="${expanded_files[0]}"
            fi
        fi
        
        if [[ -f "$config_file" ]]; then
            exists="true"
            size=$(stat -c%s "$config_file" 2>/dev/null || echo "0")
            modified=$(stat -c%Y "$config_file" 2>/dev/null || echo "0")
        fi
        
        config_files_json=$(echo "$config_files_json" | jq ".config_files += [{
            \"path\": \"$config_file\",
            \"exists\": $exists,
            \"size\": $size,
            \"modified_timestamp\": $modified
        }]")
    done
    
    echo "$config_files_json"
}

# Resume deployment from last checkpoint
resume_deployment_from_checkpoint() {
    local checkpoint_id="$1"
    
    if [[ -z "$checkpoint_id" ]]; then
        # Find the latest checkpoint
        checkpoint_id=$(ls -t "$CHECKPOINT_DIR"/*.json 2>/dev/null | head -1 | basename | sed 's/.json$//')
    fi
    
    if [[ -z "$checkpoint_id" || ! -f "$CHECKPOINT_DIR/$checkpoint_id.json" ]]; then
        log_error "No valid checkpoint found for resumption"
        return 1
    fi
    
    log_section "Resuming Deployment from Checkpoint"
    log_info "Checkpoint ID: $checkpoint_id"
    
    # Load checkpoint data
    local checkpoint_file="$CHECKPOINT_DIR/$checkpoint_id.json"
    local checkpoint_phase=$(jq -r '.phase' "$checkpoint_file")
    local checkpoint_step=$(jq -r '.step' "$checkpoint_file")
    local checkpoint_deployment_id=$(jq -r '.deployment_id' "$checkpoint_file")
    
    log_info "Resuming from phase: $checkpoint_phase (step: $checkpoint_step)"
    log_info "Original deployment ID: $checkpoint_deployment_id"
    
    # Update current state
    CURRENT_PHASE="$checkpoint_phase"
    CURRENT_STEP="$checkpoint_step"
    LAST_CHECKPOINT="$checkpoint_id"
    
    # Update state file
    local temp_file=$(mktemp)
    jq ".resumed_from_checkpoint = \"$checkpoint_id\" |
        .resume_time = \"$(date)\" |
        .current_phase = \"$checkpoint_phase\" |
        .current_step = \"$checkpoint_step\"" "$STATE_FILE" > "$temp_file" && mv "$temp_file" "$STATE_FILE"
    
    log_success "Deployment state resumed from checkpoint: $checkpoint_id"
    return 0
}

# Validate deployment state integrity
validate_deployment_state() {
    log_step "Validating deployment state integrity"
    
    local validation_errors=0
    
    # Check if state file exists and is valid JSON
    if [[ ! -f "$STATE_FILE" ]]; then
        log_error "Deployment state file not found: $STATE_FILE"
        ((validation_errors++))
    elif ! jq empty "$STATE_FILE" 2>/dev/null; then
        log_error "Deployment state file is not valid JSON"
        ((validation_errors++))
    fi
    
    # Check if checkpoint directory exists
    if [[ ! -d "$CHECKPOINT_DIR" ]]; then
        log_error "Checkpoint directory not found: $CHECKPOINT_DIR"
        ((validation_errors++))
    fi
    
    # Validate checkpoint files
    local checkpoint_count=0
    if [[ -d "$CHECKPOINT_DIR" ]]; then
        for checkpoint_file in "$CHECKPOINT_DIR"/*.json; do
            if [[ -f "$checkpoint_file" ]]; then
                if jq empty "$checkpoint_file" 2>/dev/null; then
                    ((checkpoint_count++))
                else
                    log_warning "Invalid checkpoint file: $checkpoint_file"
                fi
            fi
        done
    fi
    
    log_info "Found $checkpoint_count valid checkpoint files"
    
    # Check state consistency
    if [[ -f "$STATE_FILE" ]]; then
        local state_checkpoints=$(jq -r '.checkpoints | length' "$STATE_FILE" 2>/dev/null || echo "0")
        if [[ "$state_checkpoints" != "$checkpoint_count" ]]; then
            log_warning "State file checkpoint count ($state_checkpoints) doesn't match actual files ($checkpoint_count)"
        fi
    fi
    
    if [[ $validation_errors -eq 0 ]]; then
        log_success "Deployment state validation passed"
        return 0
    else
        log_error "Deployment state validation failed with $validation_errors errors"
        return 1
    fi
}

# Clean up old deployment states
cleanup_old_deployment_states() {
    local retention_days="${1:-7}"
    
    log_step "Cleaning up old deployment states (retention: $retention_days days)"
    
    local cleanup_count=0
    
    # Clean up old checkpoint files
    if [[ -d "$CHECKPOINT_DIR" ]]; then
        while IFS= read -r -d '' checkpoint_file; do
            if [[ $(find "$checkpoint_file" -mtime +$retention_days 2>/dev/null) ]]; then
                rm -f "$checkpoint_file"
                ((cleanup_count++))
                log_debug "Removed old checkpoint: $(basename "$checkpoint_file")"
            fi
        done < <(find "$CHECKPOINT_DIR" -name "*.json" -print0 2>/dev/null)
    fi
    
    # Clean up old state files
    local state_backup_dir="$DEPLOYMENT_STATE_DIR/backups"
    if [[ -d "$state_backup_dir" ]]; then
        while IFS= read -r -d '' state_file; do
            if [[ $(find "$state_file" -mtime +$retention_days 2>/dev/null) ]]; then
                rm -f "$state_file"
                ((cleanup_count++))
                log_debug "Removed old state backup: $(basename "$state_file")"
            fi
        done < <(find "$state_backup_dir" -name "*.json" -print0 2>/dev/null)
    fi
    
    log_success "Cleaned up $cleanup_count old deployment state files"
}

# Generate deployment state report
generate_deployment_state_report() {
    local report_file="/var/lib/hauling-deployment/deployment-state-report-$(date +%Y%m%d_%H%M%S).txt"
    
    log_step "Generating deployment state report"
    
    cat > "$report_file" << EOF
========================================
Hauling QR Deployment State Report
========================================
Generated: $(date)
Deployment ID: $DEPLOYMENT_ID
Hostname: $(hostname)

Current State:
- Phase: $CURRENT_PHASE
- Step: $CURRENT_STEP
- Last Checkpoint: $LAST_CHECKPOINT

Deployment Progress:
$(jq -r '.completed_phases[]' "$STATE_FILE" 2>/dev/null | sed 's/^/- ✓ /' || echo "No completed phases")

System Status:
- Uptime: $(uptime -p 2>/dev/null || echo 'unknown')
- Load: $(uptime | awk -F'load average:' '{print $2}' | xargs || echo 'unknown')
- Memory: $(free -h | grep Mem | awk '{print $3 "/" $2}' || echo 'unknown')
- Disk: $(df -h / | awk 'NR==2 {print $5 " used"}' || echo 'unknown')

Component Status:
$(get_component_status_json | jq -r '.components[] | "- " + .name + ": " + .status + " (v" + .version + ")"' 2>/dev/null || echo "Component status unavailable")

Service Status:
$(get_service_status_json | jq -r '.services[] | "- " + .name + ": " + .status + " (" + .enabled + ")"' 2>/dev/null || echo "Service status unavailable")

Checkpoints Available:
$(ls -t "$CHECKPOINT_DIR"/*.json 2>/dev/null | head -5 | xargs -I {} basename {} .json | sed 's/^/- /' || echo "No checkpoints available")

State File Location: $STATE_FILE
Checkpoint Directory: $CHECKPOINT_DIR
========================================
EOF
    
    log_success "Deployment state report generated: $report_file"
    echo "$report_file"
}

# Integration functions for main script
integrate_state_management_with_main_script() {
    cat << 'EOF'
# Integration instructions for deploy-hauling-qr-ubuntu.sh:

# 1. Add these functions to the main script
# 2. Initialize state management at the beginning of main():
#    init_deployment_state_management

# 3. Update each major function to set phase:
#    set_deployment_phase "system_preparation" "updating_packages"
#    # ... existing function code ...
#    complete_deployment_phase "system_preparation" "true"

# 4. Add checkpoint creation at major milestones:
#    create_deployment_checkpoint "after_nginx_installation"

# 5. Add resumption logic:
#    if [[ "$RESUME_DEPLOYMENT" == "true" ]]; then
#        resume_deployment_from_checkpoint "$RESUME_CHECKPOINT_ID"
#    fi

# 6. Add cleanup in error handler:
#    cleanup_old_deployment_states

# 7. Add command line options:
#    --resume-from CHECKPOINT_ID
#    --list-checkpoints
#    --cleanup-state
#    --state-report
EOF
}

# Command line argument parsing for state management
parse_state_management_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --resume-from)
                RESUME_DEPLOYMENT=true
                RESUME_CHECKPOINT_ID="$2"
                shift 2
                ;;
            --list-checkpoints)
                LIST_CHECKPOINTS=true
                shift
                ;;
            --cleanup-state)
                CLEANUP_STATE=true
                CLEANUP_RETENTION_DAYS="${2:-7}"
                shift 2
                ;;
            --state-report)
                GENERATE_STATE_REPORT=true
                shift
                ;;
            --validate-state)
                VALIDATE_STATE=true
                shift
                ;;
            *)
                # Pass through other arguments
                shift
                ;;
        esac
    done
}

# Execute state management commands
execute_state_management_commands() {
    if [[ "$LIST_CHECKPOINTS" == true ]]; then
        echo "Available checkpoints:"
        ls -t "$CHECKPOINT_DIR"/*.json 2>/dev/null | while read checkpoint_file; do
            local checkpoint_id=$(basename "$checkpoint_file" .json)
            local checkpoint_date=$(jq -r '.date' "$checkpoint_file" 2>/dev/null || echo "unknown")
            local checkpoint_phase=$(jq -r '.phase' "$checkpoint_file" 2>/dev/null || echo "unknown")
            echo "- $checkpoint_id ($checkpoint_date) - Phase: $checkpoint_phase"
        done
        exit 0
    fi
    
    if [[ "$CLEANUP_STATE" == true ]]; then
        cleanup_old_deployment_states "$CLEANUP_RETENTION_DAYS"
        exit 0
    fi
    
    if [[ "$GENERATE_STATE_REPORT" == true ]]; then
        report_file=$(generate_deployment_state_report)
        echo "State report generated: $report_file"
        exit 0
    fi
    
    if [[ "$VALIDATE_STATE" == true ]]; then
        validate_deployment_state
        exit $?
    fi
    
    if [[ "$RESUME_DEPLOYMENT" == true ]]; then
        resume_deployment_from_checkpoint "$RESUME_CHECKPOINT_ID"
        # Continue with normal deployment from resumed state
    fi
}

echo "Enhanced deployment state management functions loaded successfully"
echo "These functions need to be integrated into deploy-hauling-qr-ubuntu.sh"